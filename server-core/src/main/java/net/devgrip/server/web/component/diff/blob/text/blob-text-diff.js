onedev.server.blobTextDiff = {
	symbolClasses: ".cm-property, .cm-variable, .cm-variable-2, .cm-variable-3, .cm-def, .cm-meta, .cm-string, .cm-tag, .cm-attribute, cm-builtin, cm-qualifier",
	onDomReady: function(containerId, symbolTooltipId, oldRev, newRev, oldPath, newPath, 
						 callback, blameMessageCallback, markRange, openComment, 
						 annotationInfo, commentContainerId, i18nContent) {
		var $container = $("#" + containerId);
		$container.data("commentContainerId", commentContainerId);
		$container.data("callback", callback);
		$container.data("oldPath", oldPath);
		$container.data("newPath", newPath);
		$container.data("blameMessageCallback", blameMessageCallback);
		$container.data("i18nContent", i18nContent); // 存储解析后的 JSON 对象
		$container.data("symbolHover", function() {
			var revision;
			var $symbol = $(this);
			if ($symbol.hasClass("delete")) {
				revision = oldRev;
			} else {
				var $td = $symbol.closest("td");
				
				// if is deleted line or if it is on left side of split view
				if ($td.hasClass("old") && !$td.hasClass("new") || $td.next().is("td"))
					revision = oldRev;
				else
					revision = newRev;
			}
			var symbolTooltip = document.getElementById(symbolTooltipId);
			if (symbolTooltip.onMouseOverSymbol)
				symbolTooltip.onMouseOverSymbol(revision, this);
		});
		$container.data("onMouseOverContent", function() {
			if (!onedev.server.mouseState.pressed) {
				if ($(this).hasClass("left")) {
					$container.find("td.content.right").addClass("noselect");
					$container.find("td.content.left").removeClass("noselect");
				} else if ($(this).hasClass("right")) {
					$container.find("td.content.left").addClass("noselect");
					$container.find("td.content.right").removeClass("noselect");
				}
			}
		});
		$container.find("td.content").mouseover($container.data("onMouseOverContent"));
		$container.find("td.content").click(function() {
			$(".text-diff td.content").removeClass("active");
			$(this).addClass("active");
			callback("setActive");
		});

		onedev.server.blobTextDiff.initBlameTooltip(containerId, $container.find("td.blame>a.hash"));
		
		$container.selectionPopover("init", function(e) {
	    	if ($(e.target).closest(".selection-popover").length != 0) {
	    		return;
			}
			
	    	var selection = window.getSelection();

	    	if (!selection.rangeCount || !onedev.server.mouseState.position) {
	    		return "close";
	    	}

    		var firstRange = selection.getRangeAt(0).cloneRange();
    		var lastRange = selection.getRangeAt(selection.rangeCount-1).cloneRange();

    		var $start = $(firstRange.startContainer);
    		var $end = $(lastRange.endContainer);

    		/* 
    		 * offset represents offset within $start or $end node, for instance for below selection
    		 * <span>hello</span><span>world</span>
    		 *         ^                   ^
    		 * $start will be <span>hello</span> and startOffset will be index of character 'l' which is 2, 
    		 * $end will be <span>world</span> and endOffset will be index of character 'd' which will be 4  
    		 */   
    		var startOffset = firstRange.startOffset;
    		var endOffset = lastRange.endOffset;
    		
			var $startDiff;
			if ($start.hasClass("text-diff"))
				$startDiff = $start;
			else if ($start.children(".text-diff").length != 0)
				$startDiff = $start.children(".text-diff");
			else
				$startDiff = $start.closest(".text-diff");
			
			var $endDiff;
			if ($end.hasClass("text-diff"))
				$endDiff = $end;
			else if ($end.children(".text-diff").length != 0)
				$endDiff = $end.children(".text-diff");
			else
				$endDiff = $end.closest(".text-diff");
				
			// selection must be within same file
			if ($startDiff.length == 0 || $endDiff.length == 0 || !$startDiff.is($endDiff)) { 
	    		return "close";
			}
			
			var $startTd = $start.is("td.content")? $start: $start.closest(".text-diff td.content");
			var $endTd = $end.is("td.content")? $end: $end.closest(".text-diff td.content");

			// at least one side of selection must be within a table cell
			if ($startTd.length == 0 && $endTd.length == 0) {
	    		return "close";
			}

			// make sure we are processing td.content on the same side on split view
			function getTd($tr) {
				var $td = $startTd.length != 0? $startTd: $endTd;
				if ($td.hasClass("left"))
					return $tr.find("td.content.left");
				else if ($td.hasClass("right"))
					return $tr.find("td.content.right");
				else
					return $tr.find("td.content");
			}

			/*
			 * sometimes the selection returns $start and $end as td instead of 
			 * children under td, for instance:
			 * <td><span>hello</span><span>world</span></td>
			 *                                        ^
			 * in this case, both $start and $end is the whole td, and startOffset
			 * is index of first span element which is 0, and endOffset is index of 
			 * last span element plus 1 which is 2. 
			 * 
			 * this function returns the equivalent child node and offset under td. 
			 * with above example, the equivalent child node will be <span>hello</span>
			 * and offset will be 0 for selection start, while equivalent end child node
			 * is <span>world</span> with offset being 5   
			 * 
			 */
			function getNodeAndOffsetUnderTd($td, contentIndex) {
				var $contents = $td.contents();
    			for (var i=0; i<$contents.length; i++) {
    				if (i == contentIndex) {
    					return {
    						node: $($contents[i]),
    						offset: 0
    					}
    				}
    			}		    
    			var $lastContent = $contents.last();
				return {
					node: $lastContent,
					offset: $lastContent.text().length
				}
			}

			// normalize $start to be within td.content
			if ($start.parent().parent().is("td.content")) // $start might be the text node under span
				$start = $start.parent();
			if (!$start.parent().is("td.content")) { 
				var $td;
				if ($start.is("td.content")) { // this happens if we triple click to select a line in firefox 
					$td = $start;
				} else if ($start.is("tr.code")) { // this may happen if we drag mouse to select multiple lines in firefox 
					$td = getTd($start);
					startOffset = startOffset<=$td.index()? 0: $td.contents().length;
				} else if ($start.is("tr.expander") || $start.closest("tr.expander").length != 0) { // this may happen if we drag mouse over to expander line 
					var $tr = $start.is("tr.expander")? $start.next(): $start.closest("tr.expander").next();
					if ($tr.length == 0) {
			    		return "close";
					}
					$td = getTd($tr);
					startOffset = 0;
				} else {
					var $tr = $startDiff.find("tr.code").first();
					if ($tr.length == 0) {
			    		return "close";
					}
					$td = getTd($tr);
					startOffset = 0;
				}
				var nodeAndOffset = getNodeAndOffsetUnderTd($td, startOffset);
				$start = nodeAndOffset.node;
				startOffset = nodeAndOffset.offset;
			}
			
			// drag mouse over a line below expander, chrome may set $start as the line 
			// above expander, below code normalizes this case
			$startTd = $start.parent();
			var $lastContent = $startTd.contents().last();
			var noneSelected = $start.is($lastContent) && startOffset >= $lastContent.text().length;
			if (noneSelected && $startTd.parent().next().is("tr.expander")) {
				var $tr = $startTd.parent().next().next();
				if ($tr.length == 0) {
		    		return "close";
				}
				var $td = getTd($tr);
				$start = $td.contents().first();
				startOffset = 0;
			}
			
			// normalize $end to be within td.content
			if ($end.parent().parent().is("td.content"))
				$end = $end.parent();
			if (!$end.parent().is("td.content")) { 
				var $td;
				if ($end.is("td.content")) {
					$td = $end;
				} else if ($end.is("tr.code")) {
					$td = getTd($end);
					endOffset = endOffset>$td.index()? $td.contents().length: 0;
				} else if ($end.is("tr.expander") || $end.closest("tr.expander").length != 0) {
					var $tr = $end.is("tr.expander")? $end.prev(): $end.closest("tr.expander").prev();
					if ($tr.length == 0) {
			    		return "close";
					}
					$td = getTd($tr);
					endOffset = $td.contents().length;
				} else {
					var $tr = $endDiff.find("tr.code").last();
					if ($tr.length == 0) {
			    		return "close";
					}
					$td = getTd($tr);
					endOffset = $td.contents().length;
				}
				var nodeAndOffset = getNodeAndOffsetUnderTd($td, endOffset);
				$end = nodeAndOffset.node;
				endOffset = nodeAndOffset.offset;
			}
			
			// drag mouse over a line above expander, chrome may set $end as the line 
			// below expander, below code normalizes this case
			$endTd = $end.parent();
			var $firstContent = $endTd.contents().first();
			var noneSelected = $end.is($firstContent) && endOffset == 0;
			if (noneSelected && $endTd.parent().prev().is("tr.expander")) {
				var $tr = $endTd.parent().prev().prev();
				if ($tr.length == 0) {
		    		return "close";
				}
				var $td = getTd($tr);
				$end = $td.contents().last();
				endOffset = $end.text().length;
			}
			
			$startTd = $start.parent();
			$endTd = $end.parent();
			
			// selection must not span the split view
			if ($startTd.hasClass("left") && $endTd.hasClass("right") 
					|| $startTd.hasClass("right") && $endTd.hasClass("left")) { 
	    		return "close";
			}	    	
			
			var $startTr = $startTd.parent();
			var $endTr = $endTd.parent();
			if ($startTr.index() > $endTr.index()) {
	    		return "close";
			}

			function nextContent($content) {
				var $td = $content.parent();
				var array = $td.contents().toArray();
				var index = array.indexOf($content[0]);
				if (index == array.length-1) {
					var $nextTr = $td.parent().next();
					if ($nextTr.hasClass("expander")) {
						$nextTr = $nextTr.next();
					}
					if ($nextTr.length == 0)
						return undefined;
					return getTd($nextTr).contents().first();
				} else {
					return $(array[index+1]);
				}
			}
			
			function prevContent($content) {
				var $td = $content.parent();
				var array = $td.contents().toArray();
				var index = array.indexOf($content[0]);
				if (index == 0) {
					var $prevTr = $td.parent().prev();
					if ($prevTr.hasClass("expander")) {
						$prevTr = $prevTr.next();
					}
					if ($prevTr.length == 0)
						return undefined;
					return getTd($prevTr).contents().last();
				} else {
					return $(array[index-1]);
				}
			}

			function hasOldOrNewData($content) {
				var $td = $content.parent();
				return $td.attr("data-old") || $td.attr("data-new");					
			}
			
			/*
			 * continue to normalize $start and $end. This time we convert below selection:
			 * <span>begin</span><span>middle</span><span>end</span>
			 *            ^                               ^       
			 * into this selection:
			 * <span>begin</span><span>middle</span><span>end</span>
			 *                         ^     ^
			 * this normalization makes it accurate to detect invalid selections where start 
			 * and end of selection points to different revisions
			 */
			var $content = $start;
			while (!$content.is($end) && (startOffset >= $content.text().length || !hasOldOrNewData($content))) {
				$content = nextContent($content);
				if (!$content) {
		    		return "close";
				} else {
					$start = $content;
					startOffset = 0;
				}
			}

			$content = $end;
			while (!$content.is($start) && (endOffset == 0 || !hasOldOrNewData($content))) {
				$content = prevContent($content);
				if (!$content) {
		    		return "close";
				} else {
					$end = $content;
					endOffset = $content.text().length;
				}
			}

			// check if there is anything selected
			if ($start.is($end) && startOffset >= endOffset 
					|| !hasOldOrNewData($start) || !hasOldOrNewData($end)) {
	    		return "close";
			}

			position = onedev.server.mouseState.position;			

			function showInvalidSelection() {
				var $content = $("<div></div>");
				let i18n = $container.data("i18nContent");
				let invalidSelection = i18n.invalidSelection;
				$content.append(`<a class='invalid'><svg class='icon'><use xlink:href='${onedev.server.icons}#warning'/></svg> ${invalidSelection}</a>`);
				$content.children("a").attr("href", "https://docs.devgrip.net/appendix/diff-selection").attr("target", "_blank");
				return {
					position: position, 
					content: $content
				}
			}

			if ($startTr.hasClass("expanded") || $endTr.hasClass("expanded") 
					|| $startTr.nextAll("tr.expander, tr.expanded").filter($endTr.prevAll("tr.expander, tr.expanded")).length != 0
					|| $startTr.prevAll("tr.expander, tr.expanded").filter($endTr.nextAll("tr.expander, tr.expanded")).length != 0) {  
				return showInvalidSelection();
			}
			
			/*
			 * cursor.line represents line number of selection and cursor.ch represents 
			 * character index inside text of the whole line, without considering element 
			 * tags. 
			 */
    		function getCursor($node, offset) {
				$nodeTd = $node.parent();
    			var oldLine, newLine;
				if (!$nodeTd.hasClass("old") && !$nodeTd.hasClass("new") 
						|| $nodeTd.hasClass("old") && $nodeTd.hasClass("new")) {
					oldLine = $nodeTd.data("old") + 1;
					newLine = $nodeTd.data("new") + 1;
				} else if ($nodeTd.hasClass("old")) {
					oldLine = $nodeTd.data("old") + 1;
				} else {
					newLine = $nodeTd.data("new") + 1;
				}
				var $contents = $nodeTd.contents();
				var oldOffset = 0, newOffset = 0;
    			for (var i=0; i<$contents.length; i++) {
    				var $content = $($contents[i]);
    				if ($content.is($node)) {
    					if ($content.hasClass("delete")) { 
    						oldCh = oldOffset + offset;
    						newCh = undefined;
    						newLine = undefined;
    					} else if ($content.hasClass("insert")) {
    						oldCh = undefined;
    						oldLine = undefined;
    						newCh = newOffset + offset;
    					} else {
    						oldCh = oldOffset + offset;
    						newCh = newOffset + offset;
    					}
    					break;
    				} else {
    					var len = $content.text().length;
    					if ($content.hasClass("delete")) {
    						oldOffset += len;
    					} else if ($content.hasClass("insert")) {
    						newOffset += len;
    					} else {
	    					oldOffset += len;
	    					newOffset += len;
    					}
    				}
    			}
    			if ($nodeTd.hasClass("left")) {
    				newLine = newCh = undefined;
    			} else if ($nodeTd.hasClass("right")) {
    				oldLine = oldCh = undefined;
    			}
				return {
					oldLine: oldLine,
					oldCh: oldCh,
					newLine: newLine,
					newCh: newCh
				};
    		}

    		var startCursor = getCursor($start, startOffset);
    		var endCursor = getCursor($end, endOffset);

    		if (startCursor.newLine && endCursor.newLine) {
	    		callback("openSelectionPopover", Math.round(position.left), Math.round(position.top), false, 
	    				startCursor.newLine-1, startCursor.newCh, endCursor.newLine-1, endCursor.newCh);
    		} else if (startCursor.oldLine && endCursor.oldLine) {
	    		callback("openSelectionPopover", Math.round(position.left), Math.round(position.top), true, 
	    				startCursor.oldLine-1, startCursor.oldCh, endCursor.oldLine-1, endCursor.oldCh);
    		} else {
				return showInvalidSelection();
    		}			
		});
		
		let oldCoverages = annotationInfo.oldAnnotations.coverages;
		for (var line in oldCoverages) {
			if (oldCoverages.hasOwnProperty(line)) 
				onedev.server.blobTextDiff.addCoverateInfo($container, true, line, oldCoverages[line]);
		}
		
		let newCoverages = annotationInfo.newAnnotations.coverages;
		for (var line in newCoverages) {
			if (newCoverages.hasOwnProperty(line)) 
				onedev.server.blobTextDiff.addCoverateInfo($container, false, line, newCoverages[line]);
		}

		let oldProblems = annotationInfo.oldAnnotations.problems;
		for (var line in oldProblems) {			
			if (oldProblems.hasOwnProperty(line)) { 
				let oldProblemsOnLine = oldProblems[line];
				for (var i in oldProblemsOnLine) 
					oldProblemsOnLine[i].target.location.leftSide = true;
				onedev.server.blobTextDiff.addProblemInfo($container, true, line, oldProblems[line]);
			}
		}
		
		let newProblems = annotationInfo.newAnnotations.problems;
		for (var line in newProblems) {
			if (newProblems.hasOwnProperty(line)) {  
				let newProblemsOnLine = newProblems[line];
				for (var i in newProblemsOnLine) 
					newProblemsOnLine[i].target.location.leftSide = false;
				onedev.server.blobTextDiff.addProblemInfo($container, false, line, newProblems[line]);
			}
		}
		
		if (openComment) {
			$container.data("openComment", openComment);
		}
		var oldComments = annotationInfo.oldAnnotations.comments;
		for (var line in oldComments) {
		    if (oldComments.hasOwnProperty(line)) {
				let oldCommentsOnLine = oldComments[line];
				for (var i in oldCommentsOnLine) 
					oldCommentsOnLine[i].range.leftSide = true;
		    	onedev.server.blobTextDiff.addCommentIndicator($container, true, line, oldCommentsOnLine);
		    }
		}
		var newComments = annotationInfo.newAnnotations.comments;
		for (var line in newComments) {
		    if (newComments.hasOwnProperty(line)) {
				let newCommentsOnLine = newComments[line];
				for (var i in newCommentsOnLine) 
					newCommentsOnLine[i].range.leftSide = false;
		    	onedev.server.blobTextDiff.addCommentIndicator($container, false, line, newCommentsOnLine);
		    }
		}
		
		onedev.server.blobTextDiff.highlightCommentTrigger($container);				
		
		if (markRange) {
			$container.data("markRange", markRange);
			onedev.server.blobTextDiff.mark($container, markRange);	
		}			
		
		// 添加鼠标悬停显示评论图标的功能
		onedev.server.blobTextDiff.initHoverCommentIcons($container, callback);
	},
	onLoad: function(containerId, markRange) {
		var $container = $("#" + containerId);
		if (markRange && onedev.server.viewState.getFromHistory() === undefined)
			onedev.server.blobTextDiff.scrollTo($container, markRange);
		
		var $scrollParent = $container.scrollParent();
		if (!$scrollParent.data("onTextDiffScrollInstalled"))	{
			$scrollParent.data("onTextDiffScrollInstalled", true);
			$scrollParent.doneEvents("scroll", function() {
				$(".blob-text-diff").each(function() {
					onedev.server.blobTextDiff.highlightSyntax($(this));
				}, 100);
			});
		}		
		
		onedev.server.blobTextDiff.highlightSyntax($container);
	},
	highlightSyntax($container) {
		var oldBlobPath = $container.data("oldPath");
		var newBlobPath = $container.data("newPath");
		
		$container.find("tbody, tr.expander").each(function() {
			var $this = $(this);
			var $firstCodeTr;
			if ($this.is("tbody")) {
				if ($this.children().first().hasClass("code"))
					$firstCodeTr = $this.children().first();
				else
					return;
			} else if ($this.next().length != 0){
				$firstCodeTr = $this.next();
			} else {
				return;
			}
			var $lastCodeTr;
			var $nextExpanderTr = $firstCodeTr.nextAll(".expander").first();
			if ($nextExpanderTr.length != 0)
				$lastCodeTr = $nextExpanderTr.prev();
			else
				$lastCodeTr = $container.find("tbody>tr:last-child");

			var $codeTrs = $firstCodeTr.nextUntil($lastCodeTr).add($firstCodeTr).add($lastCodeTr);
			if ($codeTrs.not(".syntaxHighlighted").length == 0)
				return;
			
			var $scrollParent = $container.scrollParent();
			if ($firstCodeTr.offset().top - $scrollParent.offset().top > $scrollParent.height()
					|| $lastCodeTr.offset().top + $lastCodeTr.outerHeight() - $scrollParent.offset().top < 0) {
				return;	
			}

			var oldLines = [], newLines = [];
			
			$codeTrs.each(function() {
				var $codeTr = $(this);
				var $content = $codeTr.children(".content");
				if ($content.length == 2) {
					var $old = $content.first();
					if (!$old.hasClass("none"))
						oldLines.push($old.text());
					
					var $new = $content.last();
					if (!$new.hasClass("none"))
						newLines.push($new.text());
				} else if ($content.hasClass("old") && $content.hasClass("new")) {
					var oldPartials = [];
					var newPartials = [];
					
					$content.contents().each(function() {
						var $this = $(this);
						var text = $this.text();
						if ($this.hasClass("delete")) {
							oldPartials.push(text);
						} else if ($this.hasClass("insert")) {
							newPartials.push(text);			
						} else {
							oldPartials.push(text);
							newPartials.push(text);					
						}
					});
					oldLines.push(oldPartials.join(""));
					newLines.push(newPartials.join(""));
				} else if ($content.hasClass("old")) {
					oldLines.push($content.text());
				} else if ($content.hasClass("new")) {
					newLines.push($content.text());
				} else {
					oldLines.push($content.text());
					newLines.push($content.text());
				}
			});
			
			function saveSyntaxes(syntaxes, text, style, lineIndex, beginPos) {
				if (lineIndex != undefined && beginPos != undefined && style != null) {
			        var classNames = "cm-" + style.replace(/ +/g, " cm-");
					var lineSyntaxes = syntaxes[lineIndex];
					if (lineSyntaxes == undefined) {
						lineSyntaxes = [];
						syntaxes[lineIndex] = lineSyntaxes;
					}
					for (var i=0; i<text.length; i++)
						lineSyntaxes[i+beginPos] = classNames;
				}
			}
			
			var oldSyntaxes = [];
			var oldProcessed = false;
			var newSyntaxes = [];
			var newProcessed = false;

			function doneHighlight() {
				if (oldProcessed && newProcessed) {
					var $codeTr = $firstCodeTr;
					var oldLineIndex = 0;
					var newLineIndex = 0;
					while (true) {
						var oldLineSyntaxes = oldSyntaxes[oldLineIndex];
						var newLineSyntaxes = newSyntaxes[newLineIndex];
						$codeTr.children("td.content:not(.none)").each(function() {
							var $td = $(this);
							
							var $highlighted = $("<div></div>");
							
							var partial = [];
							var classNames = "";
							function appendPartial() {
								if (partial.length != 0) {
									var $span = $("<span></span>");
									$span.text(partial.join(""));
									$span.attr("class", classNames);
									$highlighted.append($span);
								}
							}
							
							var oldPos = newPos = 0;
							$td.contents().each(function() {
								var $content = $(this);
								var text = $content.text();
								var lineSyntaxes;
								var pos;
								if ($td.hasClass("old") && $td.hasClass("new")) {
									if ($content.hasClass("insert")) { 
										lineSyntaxes = newLineSyntaxes;
										pos = newPos;
										newPos += text.length;
									} else if ($content.hasClass("delete")) {
										lineSyntaxes = oldLineSyntaxes;
										pos = oldPos;
										oldPos += text.length;
									} else { 
										lineSyntaxes = oldLineSyntaxes;
										pos = oldPos;
										oldPos += text.length;
										newPos += text.length;
									}
								} else if ($td.hasClass("new")) {
									lineSyntaxes = newLineSyntaxes;
									pos = newPos;
									newPos += text.length;
								} else if ($td.hasClass("old")) {
									lineSyntaxes = oldLineSyntaxes;
									pos = oldPos;
									oldPos += text.length;
								} else if ($td.hasClass("right")) {
									lineSyntaxes = newLineSyntaxes;
									pos = newPos;
									oldPos += text.length;
									newPos += text.length;
								} else {
									lineSyntaxes = oldLineSyntaxes;
									pos = oldPos;
									oldPos += text.length;
									newPos += text.length;
								}
								
								for (var i=0; i<text.length; i++) {
									var currentClassNames = $content[0].className;
									if (currentClassNames == undefined)
										currentClassNames = "";
									
									if (lineSyntaxes) {
										var syntaxClassNames = lineSyntaxes[pos+i];
										if (syntaxClassNames != undefined && syntaxClassNames.length != 0) {
											if (currentClassNames.length != 0)
												currentClassNames += " ";
											currentClassNames += syntaxClassNames;	
										}
									}
									if (currentClassNames != classNames) {
										appendPartial();
										partial = [];
										classNames = currentClassNames;
									}
									partial.push(text.charAt(i));
								}
							})
							appendPartial();
							$td.html($highlighted.html());
						});
						
						var $contents = $codeTr.children("td.content");
						if ($contents.length == 2) {
							if (!$contents.first().hasClass("none"))
								oldLineIndex++;
							if (!$contents.last().hasClass("none"))
								newLineIndex++;
						} else {
							if ($contents.hasClass("old") || $contents.hasClass("equal"))
								oldLineIndex++;
							if ($contents.hasClass("new") || $contents.hasClass("equal"))
								newLineIndex++;
						}
						
						$codeTr.find(onedev.server.blobTextDiff.symbolClasses).mouseover($container.data("symbolHover"));
						if ($codeTr.is($lastCodeTr))
							break;
						$codeTr = $codeTr.next();
					}
					
					$codeTrs.addClass("syntaxHighlighted");	
				}
			}	
					
			var oldModeInfo = onedev.server.codemirror.findModeByFileName(oldBlobPath);
			if (oldModeInfo) {
				onedev.server.codemirror.highlightSyntax(
					oldLines.join("\n"), 
					oldModeInfo, 
					function(text, style, lineIndex, beginPos) {
						saveSyntaxes(oldSyntaxes, text, style, lineIndex, beginPos);
					}, 
					undefined, 
					function() {
						oldProcessed = true;
						doneHighlight();							
					}
				);				
			} else {
				oldProcessed = true;
				doneHighlight();
			}
			
			var newModeInfo = onedev.server.codemirror.findModeByFileName(newBlobPath);
			if (newModeInfo) {
				onedev.server.codemirror.highlightSyntax(
					newLines.join("\n"), 
					newModeInfo, 
					function(text, style, lineIndex, beginPos) {
						saveSyntaxes(newSyntaxes, text, style, lineIndex, beginPos);
					}, 
					undefined,
					function() {
						newProcessed = true;
						doneHighlight();							
					}
				);				
			} else {
				newProcessed = true;
				doneHighlight();
			}
		});		
	},
	initBlameTooltip: function(containerId, $hashLink) {
		var $container = $("#" + containerId);
		var alignment = {targetX: 100, targetY: 100, x: 0, y: 0};
		$hashLink.hover(function() {
			let i18n = $container.data("i18nContent");
			let loadingText = i18n.loading;
			var $commitLink = $(this);
			var $content = $commitLink.closest("tr").children("td.content");
			var oldLine = $content.data("old");
			if (oldLine == undefined)
				oldLine = -1;
			var newLine = $content.data("new");
			if (newLine == undefined)
				newLine = -1;

			var tooltipId = "blame-message-" + containerId + "_" + oldLine + "_" + newLine;
			$container.data("blameMessageCallback")(tooltipId, $(this).data("hash"));
			var $tooltip = $(`<div class='blame-message'><div class='loading'>${loadingText}</div></div>`);
			$tooltip.attr("id", tooltipId);
			$tooltip.data("trigger", this);
			$tooltip.data("alignment", alignment);
			$container.append($tooltip);
			return $tooltip;
		}, alignment);
	},
	openSelectionPopover: function(containerId, position, markRange, markUrl, markedText, loggedIn) {
		var $container = $("#" + containerId);
		let i18n = $container.data("i18nContent");
		if (!markUrl) {
			let unableToCommentHere = i18n.unableToCommentHere;
			$content = $(`<div><span class='invalid'><svg class='icon mr-1'><use xlink:href='${onedev.server.icons}#warning'/></svg> ${unableToCommentHere}</a>`);
		} else {
			let permanentLink = i18n.permanentLink;
			var $content = $(`<div><a class='permanent'><svg class='icon mr-1'><use xlink:href='${onedev.server.icons}#link'/></svg> ${permanentLink}</a>`);
			$content.children("a.permanent").attr("href", markUrl);
			let copySelectedText = i18n.copySelectedText;
			$content.append(`<a class='copy-marked'><svg class='icon mr-1'><use xlink:href='${onedev.server.icons}#copy'/></svg> ${copySelectedText}</a>`);
			var clipboard = new ClipboardJS(".copy-marked", {
			    text: function(trigger) {
			        return markedText;
			    }
			});		
			clipboard.on("success", function(e) {
				clipboard.destroy();
				$(".selection-popover").remove();
			});
			if (loggedIn) {
				let addCommentOnThisSelection = i18n.addCommentOnThisSelection;
				$content.append(`<a class='comment'><svg class='icon mr-1'><use xlink:href='${onedev.server.icons}#comment'/></svg> ${addCommentOnThisSelection}</a>`);
				$content.children("a.comment").click(function() {
					if (onedev.server.blobTextDiff.	confirmUnsavedChanges($container)) {
						$container.data("callback")("addComment", markRange.leftSide, 
								markRange.fromRow, markRange.fromColumn, markRange.toRow, markRange.toColumn);
					}
				});
			} else {
				var loginHref = $(".sign-in").attr("href");
				let loginToComment = i18n.loginToComment;
				$content.append(`<a class='comment' href='${loginHref}'><svg class='icon mr-1'><use xlink:href='${onedev.server.icons}#warning'/></svg> ${loginToComment}</a>`);
			}			
		}		
		
		$container.selectionPopover("open", {
			position: position,
			content: $content
		});
	},
	confirmUnsavedChanges: function($container) {
		let i18n = $container.data("i18nContent");
		let thereAreUnsavedChanges = i18n.thereAreUnsavedChanges;
		return $("#"+$container.data("commentContainerId")).find("form.dirty").length == 0
				|| confirm(thereAreUnsavedChanges);
	},
	expand: function(containerId, blockIndex, expandedHtml) {
		var $container = $("#" + containerId);
		var $expanderTr = $container.find(".expander" + blockIndex);
		var $prevTr = $expanderTr.prev();
		var $nextTr = $expanderTr.next();
		$expanderTr.replaceWith(expandedHtml); 
		var $expandedTrs;
		if ($prevTr.length != 0 && $nextTr.length != 0) {
			$expandedTrs = $prevTr.nextAll().filter($nextTr.prevAll());
		} else if ($prevTr.length != 0) {
			$expandedTrs = $prevTr.nextAll();
		} else {
			$expandedTrs = $nextTr.prevAll();
		}
		$expandedTrs.find("td.content").mouseover($container.data("onMouseOverContent"));
		var $firstExpandedTr = $expandedTrs.first();
		
		function processBlames(index) {
			var commitHash = $firstExpandedTr.children("td.blame").eq(index).children("a.hash").data("hash");
			var prevCommitHash;
			$firstExpandedTr.prevAll().each(function() {
				if (!prevCommitHash) {
					var $tr = $(this);
					var $prevCommitLink = $tr.children("td.blame").eq(index).children("a.hash");
					if ($prevCommitLink.length != 0) {
						prevCommitHash = $prevCommitLink.data("hash");
					}
				}
			});
			if (commitHash == prevCommitHash) {
				$firstExpandedTr.children("td.blame").eq(index).html("<div class='same-as-above'>...</div>");
			}
			
			if ($nextTr.length != 0) {
				commitHash = $nextTr.children("td.blame").eq(index).children("a.hash").data("hash");
				prevCommitHash = undefined;
				$nextTr.prevAll().each(function() {
					if (!prevCommitHash) {
						var $prevCommitLink = $(this).children("td.blame").eq(index).children("a.hash");
						if ($prevCommitLink.length != 0) {
							prevCommitHash = $prevCommitLink.data("hash");
						}
					}
				});
				if (commitHash == prevCommitHash) {
					$nextTr.children("td.blame").eq(index).html("<div class='same-as-above'>...</div>");
				}
			}
		}
		if ($firstExpandedTr.children("td.blame").length == 1) {
			processBlames(0);
		} else if ($firstExpandedTr.children("td.blame").length == 2) {
			processBlames(0);
			processBlames(1);
		}
		
		onedev.server.blobTextDiff.initBlameTooltip(containerId, $expandedTrs.find(">td.blame>a.hash"));
		onedev.server.blobTextDiff.highlightSyntax($container);
	},
	getMarkInfo: function($container, markRange) {
		var oldOrNew = markRange.leftSide?"old":"new";
		var startCursor = [markRange.fromRow+1, markRange.fromColumn];
		var endCursor = [markRange.toRow+1, markRange.toColumn];
		var $startTd = $container.find("td.content[data-" + oldOrNew + "='" + (startCursor[0]-1) + "']");
		var $endTd = $container.find("td.content[data-" + oldOrNew + "='" + (endCursor[0]-1) + "']");
		if ($startTd.length == 0) { 
			console.error("Unable to find start td!");
			$startTd = undefined;
		} else if ($startTd.length == 2) {
			if (oldOrNew == "old") {
				$startTd = $startTd.first();
			} else {
				$startTd = $startTd.last();
			}
		}
		if ($endTd.length == 0) {
			console.error("Unable to find end td!");
			$endTd = undefined;
		} else if ($endTd.length == 2) {
			if (oldOrNew == "old") {
				$endTd = $endTd.first();
			} else {
				$endTd = $endTd.last();
			}
		}
		return {
			startTd: $startTd,
			startCursor: startCursor,
			endTd: $endTd,
			endCursor: endCursor,
			oldOrNew: oldOrNew
		};
	},
	scrollTo: function($container, markRange) {
		var markInfo = onedev.server.blobTextDiff.getMarkInfo($container, markRange);
		var $startTd = markInfo.startTd;
		var $endTd = markInfo.endTd;
		if ($startTd && $endTd) {
			var $scrollParent = $startTd.scrollParent();
			var marginTop = $startTd.closest(".text-diff").parent().prev().height() + 120;
			$scrollParent.scrollTop($startTd.offset().top - $scrollParent.offset().top + $scrollParent.scrollTop()-marginTop);
		}
	},
	scrollIntoView: function($container, markRange) {
		var markInfo = onedev.server.blobTextDiff.getMarkInfo($container, markRange);
		var $startTd = markInfo.startTd;
		var $endTd = markInfo.endTd;
		if ($startTd && $endTd) 
			$startTd[0].scrollIntoViewIfNeeded();
	},
	mark: function($container, markRangeOrMarkRanges) {
		onedev.server.blobTextDiff.clearMark($container);

		var markRanges;		
		if (Array.isArray(markRangeOrMarkRanges)) {
			markRanges = markRangeOrMarkRanges;
		} else {
			markRanges = [];
			markRanges.push(markRangeOrMarkRanges);
		}

		for (let i in markRanges) {
			let markRange = markRanges[i];
			var markInfo = onedev.server.blobTextDiff.getMarkInfo($container, markRange);
			var $startTd = markInfo.startTd;
			var $endTd = markInfo.endTd;
			if ($startTd && $endTd) {
				var startCursor = markInfo.startCursor;
				var endCursor = markInfo.endCursor;
				var oldOrNew = markInfo.oldOrNew;
				var $td = $startTd;
				while (true) {
					var ch = 0;
					if (!$td.hasClass("content-mark")) {
						$td.addClass("content-mark");
					}
					$td.contents().each(function() {
						var $this = $(this);
						var text = $this.text();
						function markText(from, to) {
							var text = $this.text();
							var left = text.substring(0, from);
							var middle = text.substring(from, to);
							var right = text.substring(to);
							
							if (left.length == 0 && right.length == 0 && $this.is("span")) {
								$this.addClass("content-mark");
							} else {
								var classes;
								if ($this.is("span"))
									classes = $this.attr("class");
								else
									classes = "";
								
								var $current = $this;
								if (left.length != 0) {
									$current.after("<span></span>");
									$current = $current.next();
									$current.attr("class", classes).text(left);
								}
								
								$current.after("<span></span>");
								$current = $current.next();
								$current.attr("class", classes + " content-mark").text(middle);
								
								if (right.length != 0) {
									$current.after("<span></span>");
									$current = $current.next();
									$current.attr("class", classes).text(right);
								}
								$this.remove();
							}
						}
						if ($this.hasClass("insert") && oldOrNew == "old" 
								|| $this.hasClass("delete") && oldOrNew == "new") {
							if (!$td.is($startTd) && !$td.is($endTd)) {
								markText(0, text.length);
							} else if ($td.is($startTd) && $td.is($endTd)) {
								if (endCursor[1]>ch && startCursor[1]<ch) {
									markText(0, text.length);
								}
							} else if ($td.is($startTd)) {
								if (startCursor[1]<ch) {
									markText(0, text.length);
								}
							} else {
								if (endCursor[1]>ch) {
									markText(0, text.length);
								}
							}
						} else {
							var nextCh = ch + text.length;
							if (!$td.is($startTd) && !$td.is($endTd)) {
								markText(0, text.length);
							} else if ($td.is($startTd) && $td.is($endTd)) {
								if (endCursor[1]>ch && startCursor[1]<nextCh) {
									if (ch>=startCursor[1] && nextCh<=endCursor[1]) {
										markText(0, text.length);
									} else if (ch<startCursor[1] && nextCh>endCursor[1]) {
										markText(startCursor[1]-ch, endCursor[1]-ch);
									} else if (ch<startCursor[1]) {
										markText(startCursor[1]-ch, text.length);
									} else {
										markText(0, endCursor[1]-ch);
									}
								}
							} else if ($td.is($startTd)) {
								if (ch>=startCursor[1]) {
									markText(0, text.length);
								} else if (nextCh>startCursor[1]) {
									markText(startCursor[1]-ch, text.length);
								} 
							} else {
								if (nextCh<=endCursor[1]) {
									markText(0, text.length);
								} else if (ch<endCursor[1]) {
									markText(0, endCursor[1]-ch);
								} 
							}
							ch = nextCh;
						}
					});
					if ($td.is($endTd) || $td.length == 0) {
						break;
					} else {
						if ($startTd.hasClass("left")) {
							$td = $td.parent().next().children("td.left");
						} else if ($startTd.hasClass("right")) {
							$td = $td.parent().next().children("td.right");
						} else {
							$td = $td.parent().next().children("td.content");
						}
					}
				}			
			}
		}
	}, 
	clearMark: function($container) {
		$container.find("td.content.content-mark").each(function() {
			var $this = $(this);
			$this.removeClass("content-mark");
			$this.find("span").removeClass("content-mark");
		});
	},
	restoreMark: function($container) {
		var markRange = $container.data("markRange");
		if (markRange) {
			onedev.server.blobTextDiff.mark($container, markRange);
		} else {
			onedev.server.blobTextDiff.clearMark($container);
		}
	},
	addProblemInfo: function($container, leftSide, line, problems) {
		let oldOrNew = leftSide?"old":"new";
		$container.find("." + oldOrNew + ".problem-popover[data-line='" + line + "']").remove();
		let $lineNumTd = onedev.server.blobTextDiff.getLineNumTd($container, leftSide, line);
		
		let $trigger = $(document.createElement("a"));
		$trigger.addClass("problem-trigger");
		
		$trigger.addClass(onedev.server.codeProblem.getSeverityInfo(problems));

		$trigger.append(`<svg class='icon icon-sm'><use xlink:href='${onedev.server.icons}#exclamation-circle-o'/></svg>`);
		
		let markRanges = [];
		for (var i in problems) 
			markRanges.push(problems[i].target.location);			
		
		$trigger.mouseover(function() {
			onedev.server.blobTextDiff.mark($container, markRanges);
		}).mouseout(function() {
			onedev.server.blobTextDiff.restoreMark($container);
		});

		$trigger.mousedown(function() {
			/* 
			 * When there are many problems, initializing popover for all of them will slow down 
		 	 * load of the source view. So we initialize popover in mouse down event
			 */
			if (!$trigger.data("popoverInited")) {
				$trigger.popover({
					html: true, 
					sanitize: false, 
					placement: "top", 
					container: $container,
					content: onedev.server.codeProblem.renderProblems(problems),
					template: `<div data-line='${line}' class='${oldOrNew} popover problem-popover'><div class='arrow'></div><div class='popover-body'></div></div>`
				}).on("shown.bs.popover", function() {
					var $currentPopover = $(`.problem-popover.${oldOrNew}[data-line='${line}']`);
					$(".popover").not($currentPopover).popover("hide");
					$currentPopover.find(".problem-content").mouseover(function() {
						onedev.server.blobTextDiff.mark($container, problems[$(this).index()].target.location);
					}).mouseout(function() {
						onedev.server.blobTextDiff.restoreMark($container);
					}).each(function() {
						var problem = problems[$(this).index()];
						$(this).children(".add-comment").click(function() {
							if (onedev.server.blobTextDiff.confirmUnsavedChanges($container)) {
								$currentPopover.popover("hide");
								var range = problem.target.location;
								$container.data("callback")("addComment", leftSide, range.fromRow, range.fromColumn, 
										range.toRow, range.toColumn);
							}
						});
					});
				}).data("popoverInited", true);				
			}			
		});
		var $confusable = $lineNumTd.children(".confusable");
		if ($confusable.length != 0)
			$confusable.after($trigger);
		else
			$lineNumTd.prepend($trigger);
	},
	addCoverateInfo: function($container, leftSide, line, coverageStatus) {
		var $lineNumTd = onedev.server.blobTextDiff.getLineNumTd($container, leftSide, line);
		var cssClass = coverageStatus.toLowerCase();
		let i18n = $container.data("i18nContent");
		var title;
		if (coverageStatus == 'COVERED')
			title = i18n.coveredByTests;
		else if (coverageStatus == 'NOT_COVERED')
			title = i18n.notCoveredByAnyTest;
		else  
			title = i18n.partiallyCoveredBySomeTests;
			
		$lineNumTd.find(".coverage").addClass(cssClass).attr("data-tippy-content", title);
	},
	addCommentIndicator: function($container, leftSide, line, comments) {
		var oldOrNew = leftSide?"old":"new";
		$container.find("." + oldOrNew + ".comment-popover[data-line='" + line + "']").remove();
		var $lineNumTd = onedev.server.blobTextDiff.getLineNumTd($container, leftSide, line);
		
		var callback = $container.data("callback");

		let i18n = $container.data("i18nContent");
		let showCommentOfMarkedText = i18n.showCommentOfMarkedText;
		var $indicator = $(document.createElement("a"));
		$indicator.addClass("comment-indicator");
		$indicator.data("comments", comments);
		if (comments.length != 1) {
			/* 
			 * when there are multiple comments starts with the same line, we should 
			 * display a comment indicator which will display a comment popover with 
			 * list of comment triggers upon click, user can then click one of the 
			 * trigger link to display the actual comment content  
			 */

			var updated = false;
			var content = "";
			for (var i in comments) { 
				let cssClasses = "comment-trigger";
				if (comments[i].updated) {
					cssClasses += " updated";
					updated = true;
				}
				content += `<a class='${cssClasses}' data-tippy-content='${showCommentOfMarkedText}'>#${comments[i].id}</a>`;
			}

			if (updated)
				$indicator.addClass("updated");
			
			$indicator.append(`<svg class='icon'><use xlink:href='${onedev.server.icons}#comments'/></svg>`);
			
			$indicator.popover({
				html: true, 
				sanitize: false, 
				container: $container,
				placement: "auto",
				template: `<div class='${oldOrNew} popover comment-popover' data-line='${line}'><div class='arrow'></div><div class='popover-body'></div></div>`,
				content: content
			});
			$indicator.on('shown.bs.popover', function () {
				var $currentPopover = $(`.${oldOrNew}.comment-popover[data-line='${line}']`);
				$(".popover").not($currentPopover).popover("hide");
				
				$currentPopover.find("a").each(function() {
					$(this).mouseover(function() {
						var comment = comments[$(this).index()];			        						
						onedev.server.blobTextDiff.mark($container, comment.range);
					});
					$(this).mouseout(function() {
						onedev.server.blobTextDiff.restoreMark($container);
					});
					$(this).click(function() {
						if (!$(this).hasClass("active") && onedev.server.blobTextDiff.confirmUnsavedChanges($container)) {
							var comment = comments[$(this).index()];			        						
							callback("openComment", comment.id, comment.range.leftSide, comment.range.fromRow, 
									comment.range.fromColumn, comment.range.toRow, comment.range.toColumn);
						}
					});
				});
				onedev.server.blobTextDiff.highlightCommentTrigger($container);				
			});
		} else {
			var comment = comments[0];
			if (comment.updated)
				$indicator.addClass("updated");
			$indicator.addClass("comment-trigger").attr("data-tippy-content", showCommentOfMarkedText);
			$indicator.append("<svg class='icon'><use xlink:href='" + onedev.server.icons + "#comment'/></svg>");
			$indicator.mouseover(function() {
				onedev.server.blobTextDiff.mark($container, comment.range);
			});
			$indicator.mouseout(function() {
				onedev.server.blobTextDiff.restoreMark($container);
			});
			$indicator.click(function() {
				if (!$indicator.hasClass("active") && onedev.server.blobTextDiff.confirmUnsavedChanges($container)) {
					callback("openComment", comment.id, comment.range.leftSide, comment.range.fromRow, 
							comment.range.fromColumn, comment.range.toRow, comment.range.toColumn);
				}
			});
		}
		$lineNumTd.children(".comment-indicator").remove();
		$lineNumTd.prepend($indicator);
	},	
	getLineNumTd: function($container, leftSide, line) {
		if (leftSide) {
			var $contentTd = $container.find("td.left.content[data-old='" + line + "']");
			if ($contentTd.length == 0)
				$contentTd = $container.find("td.content[data-old='" + line + "']");
			return $contentTd.prevAll("td.number").last();
		} else {
			var $contentTd = $container.find("td.right.content[data-new='" + line + "']");
			if ($contentTd.length == 0)
				$contentTd = $container.find("td.content[data-new='" + line + "']");
			return $contentTd.prevAll("td.number").first();
		}
	},
	highlightCommentTrigger: function($container) {
		$container.find(".comment-trigger").removeClass("active");
		var openComment = $container.data("openComment");
		if (openComment) {
			var line = parseInt(openComment.range.fromRow);
			var $indicator = onedev.server.blobTextDiff.getLineNumTd($container, openComment.range.leftSide, line).children(".comment-indicator");
			if ($indicator.length != 0) {
				var comments = $indicator.data("comments");
				if (comments.length == 1) {
					$indicator.addClass("active");
				} else {
					var oldOrNew = openComment.range.leftSide?"old":"new";
					$container.find("." + oldOrNew + ".comment-popover[data-line='" + line + "'] a").each(function() {
						var comment = comments[$(this).index()];			        						
						if (comment.id == openComment.id) {
							$(this).addClass("active");
						}
					});
				}
			}
		}
	},
	onCommentAdded: function($container, comment) {
		$container.data("openComment", comment);
		$container.data("markRange", comment.range);
		
		var line = parseInt(comment.range.fromRow);		
		var leftSide = comment.range.leftSide;
		
		var $indicator = onedev.server.blobTextDiff.getLineNumTd($container, leftSide, line).children(".comment-indicator");
		var comments;
		if ($indicator.length != 0) {
			comments = $indicator.data("comments");
		} else {
			comments = [];
		} 
		comments.push(comment);
		onedev.server.blobTextDiff.addCommentIndicator($container, leftSide, line, comments);
		onedev.server.blobTextDiff.highlightCommentTrigger($container);				
	},
	onCommentDeleted: function($container) {
		$(".popover").popover("hide");
		
		var openComment = $container.data("openComment");
		if (openComment) {
			$container.removeData("openComment");
			
			var line = parseInt(openComment.range.fromRow);
			var leftSide = openComment.range.leftSide;
			var $indicator = onedev.server.blobTextDiff.getLineNumTd($container, leftSide, line).children(".comment-indicator");
			var comments = $indicator.data("comments");
			if (comments.length == 1) {
				$indicator.remove();
			} else {
				for (var i in comments) {
					var each = comments[i];
					if (each.id == openComment.id) {
						comments.splice(i, 1);
						break;
					}
				}
				onedev.server.blobTextDiff.addCommentIndicator($container, leftSide, line, comments);
			}
			onedev.server.blobTextDiff.highlightCommentTrigger($container);				
		}
	},
	onCommentOpened: function($container, comment) {
		$(".popover").popover("hide");
		$container.data("openComment", comment);
		$container.data("markRange", comment.range);
		onedev.server.blobTextDiff.highlightCommentTrigger($container);
		onedev.server.blobTextDiff.mark($container, comment.range);
		onedev.server.blobTextDiff.scrollIntoView($container, comment.range);
	},
	onCommentClosed: function($container) {
		$(".popover").popover("hide");
		$container.removeData("openComment");
		onedev.server.blobTextDiff.highlightCommentTrigger($container);
	},
	onAddComment: function($container, markRange) {
		$(".popover").popover("hide");
		$container.removeData("openComment");
		$container.data("markRange", markRange);
		
		$container.selectionPopover("close");
		window.getSelection().removeAllRanges();

		// continue to operate DOM in a timer to give browser a chance to 
		// clear selections
		setTimeout(function() {
			onedev.server.blobTextDiff.highlightCommentTrigger($container);
			onedev.server.blobTextDiff.mark($container, markRange);
			onedev.server.blobTextDiff.scrollIntoView($container, markRange);
		}, 100);
	},
	
	// 初始化鼠标悬停显示评论图标功能
	initHoverCommentIcons: function($container, callback) {
		// 先移除之前的事件绑定，避免重复绑定
		$container.off("mouseenter", "tr.code");
		$container.off("mouseleave", "tr.code");
		
		// 鼠标悬停事件处理
		$container.on("mouseenter", "tr.code", function(e) {
			var $tr = $(this);
			var $contentTd = $tr.find("td.content");
			
			// 检查该行是否已有评论图标
			var oldLine = $contentTd.first().data("old");
			var newLine = $contentTd.last().data("new");
			
			// 检查是否有评论图标
			var hasCommentIndicator = $tr.find(".comment-indicator").length > 0;
			if (hasCommentIndicator) {
				return; // 如果已有评论图标，不显示悬停图标
			}
			
			// 创建悬停评论图标
			let i18n = $container.data("i18nContent");
			let addCommentToThisLine = i18n.addCommentToThisLine;
			var $hoverIcon = $('<a class="hover-comment-icon" title="' + addCommentToThisLine + '"><svg class="icon"><use xlink:href="' + onedev.server.icons + '#comment"/></svg></a>');
			// 为每个行号单元格添加悬停图标，但只在实际有内容的一侧显示
			$tr.find("td.number").each(function(index) {
				var $lineNumber = $(this);
				var isLeftSide = index === 0; // 第一个td.number是左侧
				
				// 检查该侧是否有实际内容
				var hasContent = false;
				if (isLeftSide) {
					// 检查左侧是否有内容（不是none类）
					var $leftContent = $tr.find("td.content.left");
					if ($leftContent.length > 0 && !$leftContent.hasClass("none")) {
						hasContent = true;
					} else {
						// 检查统一视图中的内容
						var $content = $tr.find("td.content");
						if ($content.length === 1 && $content.data("old") !== undefined) {
							hasContent = true;
						}
					}
				} else {
					// 检查右侧是否有内容（不是none类）
					var $rightContent = $tr.find("td.content.right");
					if ($rightContent.length > 0 && !$rightContent.hasClass("none")) {
						hasContent = true;
					} else {
						// 检查统一视图中的内容
						var $content = $tr.find("td.content");
						if ($content.length === 1 && $content.data("new") !== undefined) {
							hasContent = true;
						}
					}
				}
				
				// 只有有内容的一侧才显示悬停图标
				if (hasContent) {
					// 确保行号容器有相对定位
					$lineNumber.css('position', 'relative');
					
					$hoverIcon.clone().css({
						position: 'absolute',
						left: '4px',
						top: '50%',
						transform: 'translateY(-50%)',
						zIndex: 1000,
						opacity: 0.7,
						cursor: 'pointer',
						display: 'block',
						width: '16px',
						height: '16px',
						textAlign: 'center',
						lineHeight: '16px'
					}).appendTo($lineNumber);
				}
			});
			
			// 为悬停图标添加点击事件
			$tr.find(".hover-comment-icon").on("click", function(e) {
				e.preventDefault();
				e.stopPropagation();
				
				if (onedev.server.blobTextDiff.confirmUnsavedChanges($container)) {
					// 确定是哪一侧的行（左侧或右侧）
					var $clickedTd = $(this).closest("td.number");
					var isLeftSide = $clickedTd.index() === 0; // 第一个td.number是左侧
					
					var line;
					var leftSide;
					
					if (isLeftSide) {
						// 检查左侧内容
						var $leftContent = $tr.find("td.content.left");
						if ($leftContent.length > 0 && !$leftContent.hasClass("none")) {
							line = $leftContent.data("old");
							leftSide = true;
						} else {
							// 检查统一视图中的内容
							var $content = $tr.find("td.content");
							if ($content.length === 1 && $content.data("old") !== undefined) {
								line = $content.data("old");
								leftSide = true;
							}
						}
					} else {
						// 检查右侧内容
						var $rightContent = $tr.find("td.content.right");
						if ($rightContent.length > 0 && !$rightContent.hasClass("none")) {
							line = $rightContent.data("new");
							leftSide = false;
						} else {
							// 检查统一视图中的内容
							var $content = $tr.find("td.content");
							if ($content.length === 1 && $content.data("new") !== undefined) {
								line = $content.data("new");
								leftSide = false;
							}
						}
					}
					
					if (line !== undefined) {
						// 创建整行评论范围
						var commentRange = {
							leftSide: leftSide,
							fromRow: line,
							fromColumn: 0,
							toRow: line,
							toColumn: 1000 // 使用一个大的数字表示整行
						};
						
						// 调用添加评论回调
						callback("addComment", leftSide, commentRange.fromRow, commentRange.fromColumn, 
								commentRange.toRow, commentRange.toColumn);
					}
				}
			});
			
			// 鼠标悬停效果
			$tr.find(".hover-comment-icon").hover(
				function() {
					$(this).css('opacity', '1');
				},
				function() {
					$(this).css('opacity', '0.7');
				}
			);
		});
		
		// 鼠标离开事件处理
		$container.on("mouseleave", "tr.code", function(e) {
			var $tr = $(this);
			
			// 移除悬停图标
			$tr.find(".hover-comment-icon").remove();
		});
	}
};

$(function() {
	$(document).keydown(function(e) {
		if (e.keyCode == 27) 
			$('.problem-popover').popover("hide");
	}).mouseup(function(e) {
		if ($(e.target).closest(".problem-popover, .problem-trigger").length == 0)		
			$('.problem-popover').popover("hide");
	});
});
