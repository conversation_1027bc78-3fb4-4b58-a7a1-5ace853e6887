.source-view>div.code {
	background: none;
	border: none;
	border-radius: 0;
	padding: 0;
	margin: 0;
	font-family: inherit;
	font-size: inherit;
}
.source-view>.comment>.ui-resizable-handle {
	border-right: 1px solid var(--light-gray);
	background: var(--light) url(/~icon/grip3.svg) no-repeat scroll center center;
	background-size: 18px 18px;
    cursor: e-resize;
    width: 4px;
	position: relative;
	right: 0;
}
.dark-mode .source-view>.comment>.ui-resizable-handle {
	border-right-color: var(--dark-mode-lighter-dark);
	background: var(--dark-mode-light-dark) url(/~icon/dark-grip3.svg) no-repeat scroll center center;
	background-size: 18px 18px;
}
.source-view>.outline {
	left: 0 !important;
}
.source-view>.outline>.ui-resizable-handle {
	border-left: 1px solid var(--light-gray);
	background: var(--light) url(/~icon/grip3.svg) no-repeat scroll center center;
	background-size: 18px 18px;
    cursor: w-resize;
    width: 4px;
	position: relative;
	left: 0;
}
.dark-mode .source-view>.outline>.ui-resizable-handle {
	border-left-color: var(--dark-mode-lighter-dark);
	background: var(--dark-mode-light-dark) url(/~icon/dark-grip3.svg) no-repeat scroll center center;
	background-size: 18px 18px;
}
.source-view>.outline>.content {
	padding: 0.5rem 1rem;
}
.source-view>.outline>.content .tree-content>a {
	display: inline-block;
	border-radius: 4px;
	padding: 1px 4px 1px 3px;
}
.source-view>.outline>.content .tree-content>a.active {
	background: #FCF5DA;
}
.dark-mode .source-view>.outline>.content .tree-content>a.active {
	background: var(--dark-mode-light-warning);
}

.CodeMirror-linenumber {
	width: 30px;
	padding-left: 0;
}
.CodeMirror-coverages {
	width: 6px;
}
.CodeMirror-coverage {
	margin-right: 2px;
}
.CodeMirror-coverage.covered {
	background: var(--success);
}
.CodeMirror-coverage.partially_covered {
	background: var(--warning);
}
.CodeMirror-coverage.not_covered {
	background: var(--danger);
}
.CodeMirror-coverage.covered:hover {
	background: #12827c;
}
.CodeMirror-coverage.partially_covered:hover {
	background: #b37600;
}
.CodeMirror-coverage.not_covered:hover {
	background: #BF3C4A;
}

.CodeMirror-comments {
	width: 24px;
}
.CodeMirror-problems {
	width: 20px;
}
.CodeMirror-comment, .CodeMirror-problem {
	text-align: right;
}

.CodeMirror-annotations {
	width: 240px;
}
.CodeMirror-annotation {
	font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	padding: 0 4px;
}
.source-view .CodeMirror .CodeMirror-cursor {
	border-left: none;
}
.CodeMirror-annotation .hash, .CodeMirror-annotation .date {
	margin-right: 8px;
}
.CodeMirror-annotation .same-as-above {
	margin-left: 100px;
	color: var(--gray);
}
.CodeMirror-activeline .CodeMirror-annotation {
	background: #E8F2FE;	
}
.dark-mode .CodeMirror-activeline .CodeMirror-annotation {
	background: var(--gray-dark);	
}

.outline-search>.modal-body>.content {
	height: 480px;
}
